package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.pp;

import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import fm.lizhi.pp.content.assistant.api.PendantService;
import fm.lizhi.pp.content.assistant.protocol.PendantServiceProto.PendantProbuf;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import org.apache.zookeeper.AsyncCallback.VoidCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PpPendantRemote implements PendantRemote {


    @Autowired
    private PendantService ppPendantService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        PendantProbuf pendantProbuf = PendantProbuf.newBuilder()
                .setId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .build();
        Result<Void> result = ppPendantService.updatePendant(pendantProbuf);
        if (RpcResult.isFail(result)) {
            log.warn("pp updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return RpcResult.fail(UPDATE_PENDANT_SHOW_TIME_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        
        Result<Void> result = ppPendantService.updatePendantStatus(param.getPendantId(), param.getStatus().getStatus());
        if (RpcResult.isFail(result)) {
            log.warn("pp updatePendantStatus fail. rCode={},pendantId={},status={}", result.rCode(), param.getPendantId(), param.getStatus());
            return RpcResult.fail(UPDATE_PENDANT_STATUS_FAIL);
        }
        return RpcResult.success();
    }

}
