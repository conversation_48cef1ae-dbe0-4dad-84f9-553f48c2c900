package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.singleton;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.hy.content.api.ConditionService;
import fm.lizhi.hy.content.api.PendantService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;

@Configuration
public class PendantServiceProvider {

    @Bean
    public PendantService hyPendantRemote() {
        return new DubboClientBuilder<>(PendantService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.pp.content.assistant.api.PendantService ppPendantRemote() {
        return new DubboClientBuilder<>(fm.lizhi.pp.content.assistant.api.PendantService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.content.api.PendantService xmPendantRemote() {
        return new DubboClientBuilder<>(fm.lizhi.xm.content.api.PendantService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public ConditionService hyConditionService() {
        return new DubboClientBuilder<>(ConditionService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.pp.content.assistant.api.ConditionService ppConditionService() {
        return new DubboClientBuilder<>(fm.lizhi.pp.content.assistant.api.ConditionService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.content.api.ConditionService xmConditionService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.content.api.ConditionService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
