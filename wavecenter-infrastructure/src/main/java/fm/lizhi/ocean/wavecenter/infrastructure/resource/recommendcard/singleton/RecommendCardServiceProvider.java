package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.live.room.recommendation.api.PpRecommendationCardManageService;
import fm.lizhi.live.room.xm.api.RecommendationCardManageService;
import fm.lizhi.live.room.xm.services.RecommendationCardSpringService;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import hy.fm.lizhi.live.pp.live.api.TopRecommendH5Service;
import hy.fm.lizhi.live.pp.live.api.TopRecommendService;
import hy.fm.lizhi.live.pp.live.api.TopRecommendSpringService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:29
 */
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = TopRecommendService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = TopRecommendSpringService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = TopRecommendH5Service.class),
})
@Configuration
public class RecommendCardServiceProvider {

    @Bean
    public fm.lizhi.live.room.pp.api.RecommendCardService recommendCardService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.pp.api.RecommendCardService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    @ConditionalOnMissingBean(value = RecommendationCardSpringService.class)
    public RecommendationCardSpringService recommendationCardService(){
        return new DubboClientBuilder<>(RecommendationCardSpringService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public RecommendationCardManageService recommendationCardManageService(){
        return new DubboClientBuilder<>(RecommendationCardManageService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    @ConditionalOnMissingBean(value = PpRecommendationCardManageService.class)
    public PpRecommendationCardManageService ppRecommendationCardManageService(){
        return new DubboClientBuilder<>(PpRecommendationCardManageService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
