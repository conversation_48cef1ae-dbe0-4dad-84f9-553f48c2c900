package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;

public interface PendantRemote extends IRemote {

    /**
     * 更新挂件展示时间
     * @param param
     * @return
     */
    Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param);

    /**
     * 更新挂件状态
     * @param param
     * @return
     */
    Result<Void> updatePendantStatus(PendantStatusParamDTO param);

    /**
     * 更新挂件展示时间失败
     */
    int UPDATE_PENDANT_SHOW_TIME_FAIL = 1;

    /**
     * 更新挂件状态失败
     */
    int UPDATE_PENDANT_STATUS_FAIL = 2;
}
