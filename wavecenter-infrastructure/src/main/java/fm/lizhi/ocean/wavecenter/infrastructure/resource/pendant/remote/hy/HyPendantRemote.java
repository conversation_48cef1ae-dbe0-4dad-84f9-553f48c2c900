package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.content.api.PendantService;
import fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HyPendantRemote implements PendantRemote {


    @Autowired
    private PendantService pendantService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        PendantProbuf pendantProbuf = PendantProbuf.newBuilder()
                .setId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .build();
        Result<Void> result = pendantService.updatePendant(pendantProbuf);
        if (RpcResult.isFail(result)) {
            log.warn("hy updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return RpcResult.fail(UPDATE_PENDANT_SHOW_TIME_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        Result<Void> result = pendantService.updatePendantStatus(param.getPendantId(), param.getStatus().getStatus());
        if (RpcResult.isFail(result)) {
            log.warn("hy updatePendantStatus fail. rCode={},pendantId={},status={}", result.rCode(), param.getPendantId(), param.getStatus());
            return RpcResult.fail(UPDATE_PENDANT_STATUS_FAIL);
        }
        return RpcResult.success();
    }

}
