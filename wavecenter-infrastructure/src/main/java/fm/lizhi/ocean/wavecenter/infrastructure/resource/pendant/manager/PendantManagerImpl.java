package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.manager.PendantManager;
import groovy.util.logging.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PendantManagerImpl implements PendantManager {


    @Autowired
    private PendantRemote pendantRemote;

    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        return pendantRemote.updatePendantShowTime(param);
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        return pendantRemote.updatePendantStatus(param);
    }

}
