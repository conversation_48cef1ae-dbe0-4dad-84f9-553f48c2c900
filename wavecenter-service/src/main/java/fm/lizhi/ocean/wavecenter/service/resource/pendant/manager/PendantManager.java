package fm.lizhi.ocean.wavecenter.service.resource.pendant.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;

public interface PendantManager {

    /**
     * 更新挂件展示时间
     * @param param
     * @return 结果
     */
    Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param);

    /**
     * 更新挂件状态
     * @param param
     * @return
     * @return 结果
     */
    Result<Void> updatePendantStatus(PendantStatusParamDTO param);
}
