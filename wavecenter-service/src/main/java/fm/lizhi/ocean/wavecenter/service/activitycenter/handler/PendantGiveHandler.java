package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.BannerExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ResourceExtraMapping;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 挂件发放处理器
 */
@Slf4j
@Component
public class PendantGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private LiveManager liveManager;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        try {
            ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();

            // todo 调用挂件设置接口
            return RpcResult.success();
        } catch (Exception e) {
            log.error("PendantGiveHandler.giveFlowResource happen error: context={}", JSONObject.toJSONString(context), e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.PANDANT_GIVE_FAIL);
        }
    }

    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
//        DeleteBannerParamDTO paramDTO = new DeleteBannerParamDTO();
//        paramDTO.setId(param.getBizRecordId());
//        return activityMaterielManager.deleteBannerConfig(paramDTO);

        // todo 调用取消挂件发放

        return RpcResult.success();
    }

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.PENDANT.getResourceCode();
    }

}
