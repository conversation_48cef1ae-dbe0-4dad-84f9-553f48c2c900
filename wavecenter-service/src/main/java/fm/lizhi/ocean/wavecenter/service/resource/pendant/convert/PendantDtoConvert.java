package fm.lizhi.ocean.wavecenter.service.resource.pendant.convert;

import fm.lizhi.ocean.wavecenter.api.resource.pendant.bean.GetPendantsBean;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 挂件数据转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface PendantDtoConvert {

    PendantDtoConvert I = Mappers.getMapper(PendantDtoConvert.class);

    /**
     * 将 PendantDto 转换为 GetPendantsBean
     *
     * @param pendantDto 挂件DTO
     * @return 挂件响应对象
     */
    GetPendantsBean toGetPendantsBean(PendantDto pendantDto);

    /**
     * 将 PendantDto 列表转换为 GetPendantsBean 列表
     *
     * @param pendantDtoList 挂件DTO列表
     * @return 挂件响应对象列表
     */
    List<GetPendantsBean> toGetPendantsBeans(List<PendantDto> pendantDtoList);
}