package fm.lizhi.ocean.wavecenter.service.resource.pendant.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.pendant.PendantService;
import fm.lizhi.ocean.wavecenter.api.resource.pendant.bean.GetPendantsBean;
import fm.lizhi.ocean.wavecenter.api.resource.pendant.request.RequestGetPendants;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.convert.PendantDtoConvert;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.manager.PendantManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 挂件服务实现类
 *
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class PendantServiceImpl implements PendantService {

    @Autowired
    private PendantManager pendantManager;

    @Override
    public Result<List<GetPendantsBean>> getPendants(RequestGetPendants request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        try {
            List<PendantDto> pendantDtoList = pendantManager.getByIds(request.getIds(), request.getAppId());
            List<GetPendantsBean> result = PendantDtoConvert.I.toGetPendantsBeans(pendantDtoList);

            if (log.isDebugEnabled()) {
                log.debug("getPendants success, result={}", JsonUtils.toJsonString(result));
            }

            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("getPendants error, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(26302, "查询挂件失败");
        }
    }
}