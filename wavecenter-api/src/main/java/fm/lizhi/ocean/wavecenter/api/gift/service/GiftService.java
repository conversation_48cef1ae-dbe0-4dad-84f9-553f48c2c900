package fm.lizhi.ocean.wavecenter.api.gift.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestGetGifts;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;

import javax.validation.Valid;
import java.util.List;

/**
 * 礼物服务服务, 前接口错误码以262头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface GiftService {

    /**
     * 分页查询礼物列表
     *
     * @param request 请求参数
     * @return 查询结果
     */
    Result<PageBean<ListGiftBean>> listGift(@Valid RequestListGift request);

    /**
     * 批量查询礼物
     *
     * @param request 请求参数
     * @return 查询结果
     */
    Result<List<GetGiftsBean>> getGifts(@Valid RequestGetGifts request);

    // ------------------ 方法00, listGift ------------------

    // ------------------ 方法01, getGifts ------------------
}
