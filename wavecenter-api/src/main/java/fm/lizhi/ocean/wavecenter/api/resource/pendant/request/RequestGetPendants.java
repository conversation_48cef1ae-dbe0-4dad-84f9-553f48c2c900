package fm.lizhi.ocean.wavecenter.api.resource.pendant.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量查询挂件请求参数
 *
 * <AUTHOR>
 */
@Data
public class RequestGetPendants {

    /**
     * 挂件ID列表
     */
    @NotNull(message = "挂件ID列表不能为空")
    @Size(min = 1, max = 100, message = "挂件ID列表长度必须在1-100之间")
    private List<Long> ids;

    /**
     * 应用ID：PP、ximi、heiye
     */
    @NotNull(message = "应用ID不能为空")
    private String appId;
}